import { useState, useEffect, useCallback, memo, useMemo } from "react";
import PropTypes from "prop-types";
import {
  Box,
  Typography,
  Card,
  CardContent,
  Chip,
  Button,
  CircularProgress,
  Collapse,
  IconButton,
  useTheme,
  alpha,
  LinearProgress,
  Tooltip,
  Paper,
  Stack,
  CardHeader
} from "@mui/material";
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Refresh as RefreshIcon,
  Schedule as ScheduleIcon,
  ThumbUp as ThumbUpIcon,
  Psychology as AIIcon,
  Speed as PerformanceIcon,
  Group as AudienceIcon,
  Article as ContentIcon,
  CompareArrows as CompetitiveIcon,
  Warning as WarningIcon,
  CheckCircle as SuccessIcon,
  Info as InfoIcon,
  PlayArrow as ActionIcon,
  TrendingUp as TrendingUpIcon
} from "@mui/icons-material";

// Enhanced service imports
import { aiInsightsDataService } from "../../services/aiInsightsDataService";
import { openAIInsightsService } from "../../services/openAIInsightsService";
import { useSubscription } from "../../contexts/SubscriptionContext";
import { useNotification } from "../../hooks/useNotification";

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Insight category configurations
const INSIGHT_CATEGORIES = {
  content: {
    icon: ContentIcon,
    color: ACE_COLORS.PURPLE,
    label: 'Content Strategy'
  },
  timing: {
    icon: ScheduleIcon,
    color: ACE_COLORS.YELLOW,
    label: 'Optimal Timing'
  },
  audience: {
    icon: AudienceIcon,
    color: ACE_COLORS.PURPLE,
    label: 'Audience Insights'
  },
  engagement: {
    icon: ThumbUpIcon,
    color: ACE_COLORS.YELLOW,
    label: 'Engagement'
  },
  competitive: {
    icon: CompetitiveIcon,
    color: ACE_COLORS.PURPLE,
    label: 'Competitive Intelligence'
  },
  performance: {
    icon: PerformanceIcon,
    color: ACE_COLORS.YELLOW,
    label: 'Performance'
  }
};

// Insight type configurations
const INSIGHT_TYPES = {
  positive: { icon: SuccessIcon, color: 'success.main' },
  negative: { icon: WarningIcon, color: 'error.main' },
  neutral: { icon: InfoIcon, color: 'info.main' },
  opportunity: { icon: TrendingUpIcon, color: 'warning.main' },
  warning: { icon: WarningIcon, color: 'error.main' }
};

/**
 * Enhanced QuickInsights Component - AI-powered social media recommendations
 *
 * Features:
 * - OpenAI integration for intelligent insights
 * - Subscription-based recommendation quality
 * - Categorized insights with confidence scores
 * - Actionable recommendations with next steps
 * - Real-time data aggregation and caching
 * - Enterprise-grade error handling and fallbacks
 *
 * @component
 * @param {Object} props - Component props
 * @param {Object} props.data - Analytics data from dashboard
 * @param {Function} props.onRefresh - Refresh callback
 * @param {boolean} props.loading - Loading state
 * @param {Object} props.error - Error object
 * @param {boolean} props.showRefresh - Show refresh button
 * @param {number} props.maxInsights - Maximum insights to display
 * @param {boolean} props.autoRefresh - Enable auto refresh
 * @param {number} props.refreshInterval - Auto refresh interval
 * @param {Object} props.planFeatures - Subscription plan features
 * @param {boolean} props.enableAccessibility - Enable accessibility features
 * @param {string} props.testId - Test identifier
 */
const QuickInsights = memo(({
  data,
  onRefresh,
  loading = false,
  error = null,
  showRefresh = true,
  maxInsights = 5,
  autoRefresh = false,
  refreshInterval = 300000, // 5 minutes
  planFeatures = null,
  enableAccessibility = true,
  testId = 'quick-insights'
}) => {
  const theme = useTheme();

  // Enhanced context integration
  const { subscription, hasFeatureAccess } = useSubscription();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  // Enhanced state management
  const [expanded, setExpanded] = useState(true);
  const [aiInsights, setAiInsights] = useState(null);
  const [aiLoading, setAiLoading] = useState(false);
  const [aiError, setAiError] = useState(null);
  const [lastRefresh, setLastRefresh] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showActions, setShowActions] = useState(false);
  // Enhanced AI insights generation
  const generateAIInsights = useCallback(async (forceRefresh = false) => {
    if (!data || aiLoading) return;

    try {
      setAiLoading(true);
      setAiError(null);

      if (enableAccessibility) {
        // Announce to screen readers
        const announcement = 'Generating AI insights based on your analytics data';
        if (window.speechSynthesis) {
          const utterance = new SpeechSynthesisUtterance(announcement);
          window.speechSynthesis.speak(utterance);
        }
      }

      // Aggregate comprehensive metrics data
      const aggregatedData = await aiInsightsDataService.aggregateMetricsData(
        subscription,
        { forceRefresh, includeRealTime: true }
      );

      // Generate AI insights using OpenAI
      const insights = await openAIInsightsService.generateInsights(
        aggregatedData,
        subscription,
        {
          maxInsights: planFeatures?.maxInsights || maxInsights,
          categories: selectedCategory === 'all' ? null : [selectedCategory]
        }
      );

      setAiInsights(insights);
      setLastRefresh(new Date());

      if (enableAccessibility) {
        const announcement = `Generated ${insights.insights?.length || 0} AI recommendations`;
        if (window.speechSynthesis) {
          const utterance = new SpeechSynthesisUtterance(announcement);
          window.speechSynthesis.speak(utterance);
        }
      }

      showSuccessNotification('AI insights updated successfully');

      // Track analytics
      if (window.gtag) {
        window.gtag('event', 'ai_insights_generated', {
          event_category: 'ai_features',
          event_label: subscription?.plan_id || 'creator',
          value: insights.insights?.length || 0,
          insights_count: insights.insights?.length || 0,
          confidence_score: insights.metadata?.confidence || 0
        });
      }

    } catch (error) {
      console.error('Failed to generate AI insights:', error);
      setAiError(error.message);
      showErrorNotification('Failed to generate AI insights. Please try again.');

      // Track error
      if (window.gtag) {
        window.gtag('event', 'ai_insights_error', {
          event_category: 'ai_features',
          event_label: 'generation_failed',
          error_message: error.message
        });
      }
    } finally {
      setAiLoading(false);
    }
  }, [data, subscription, planFeatures, maxInsights, selectedCategory, aiLoading,
      enableAccessibility, showSuccessNotification, showErrorNotification]);

  // Enhanced data processing with subscription awareness
  const processedInsights = useMemo(() => {
    if (!aiInsights?.insights) return [];

    const planId = subscription?.plan_id || 'creator';
    const maxAllowed = planFeatures?.maxInsights ||
                      (planId === 'dominator' ? 8 : planId === 'accelerator' ? 5 : 3);

    let filtered = aiInsights.insights;

    // Filter by category if selected
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(insight => insight.category === selectedCategory);
    }

    // Apply subscription limits
    filtered = filtered.slice(0, maxAllowed);

    // Sort by confidence and impact
    return filtered.sort((a, b) => {
      const aScore = (a.confidence || 0) * (a.impact === 'high' ? 3 : a.impact === 'medium' ? 2 : 1);
      const bScore = (b.confidence || 0) * (b.impact === 'high' ? 3 : b.impact === 'medium' ? 2 : 1);
      return bScore - aScore;
    });
  }, [aiInsights, selectedCategory, subscription, planFeatures]);

  // Enhanced auto-refresh with subscription awareness
  useEffect(() => {
    if (autoRefresh && hasFeatureAccess('real_time_insights')) {
      const interval = setInterval(() => {
        generateAIInsights(false);
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, generateAIInsights, hasFeatureAccess]);

  // Initial insights generation
  useEffect(() => {
    if (data && !aiInsights && !aiLoading) {
      generateAIInsights(false);
    }
  }, [data, aiInsights, aiLoading, generateAIInsights]);

  // Legacy insights function removed - using enhanced AI insights instead

  // Auto-refresh effect (handled by enhanced AI insights generation)
  // This is now managed by the generateAIInsights function with subscription awareness

  // Enhanced refresh handler
  const handleRefresh = useCallback(() => {
    generateAIInsights(true);
    if (onRefresh) {
      onRefresh();
    }
  }, [generateAIInsights, onRefresh]);

  // Handle category filter change
  const handleCategoryChange = useCallback((category) => {
    setSelectedCategory(category);

    if (window.gtag) {
      window.gtag('event', 'insights_category_filter', {
        event_category: 'user_interaction',
        event_label: category,
        user_plan: subscription?.plan_id || 'creator'
      });
    }
  }, [subscription?.plan_id]);

  // Handle action button click
  const handleActionClick = useCallback((action, insight) => {
    if (window.gtag) {
      window.gtag('event', 'insights_action_click', {
        event_category: 'user_interaction',
        event_label: action.title,
        insight_category: insight.category,
        insight_confidence: insight.confidence,
        user_plan: subscription?.plan_id || 'creator'
      });
    }

    // You can add navigation or action handling logic here
    console.log('Action clicked:', action, 'for insight:', insight);
  }, [subscription?.plan_id]);

  // Legacy helper functions removed - using enhanced insight type configurations instead

  return (
    <Card
      sx={{
        overflow: "hidden",
        height: "auto",
        minHeight: "100%",
        display: "flex",
        flexDirection: "column",
        position: "relative",
        background: `linear-gradient(135deg, ${alpha(ACE_COLORS.PURPLE, 0.05)} 0%, ${alpha(ACE_COLORS.YELLOW, 0.05)} 100%)`,
        border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}`
      }}
      data-testid={testId}
    >
      {/* Enhanced Header with AI Branding */}
      <CardHeader
        avatar={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <AIIcon sx={{ color: ACE_COLORS.PURPLE, fontSize: 28 }} />
            {aiLoading && (
              <CircularProgress size={16} sx={{ color: ACE_COLORS.PURPLE }} />
            )}
          </Box>
        }
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="h6" sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
              AI Insights & Recommendations
            </Typography>
            {aiInsights?.metadata && (
              <Chip
                label={`${subscription?.plan_id || 'creator'} plan`}
                size="small"
                sx={{
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                  color: ACE_COLORS.PURPLE,
                  fontWeight: 500
                }}
              />
            )}
          </Box>
        }
        subheader={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 0.5 }}>
            {lastRefresh && (
              <Typography variant="caption" color="text.secondary">
                Last updated: {lastRefresh.toLocaleTimeString()}
              </Typography>
            )}
            {aiInsights?.metadata?.confidence && (
              <Tooltip title="AI Confidence Score">
                <Chip
                  label={`${Math.round(aiInsights.metadata.confidence * 100)}% confidence`}
                  size="small"
                  variant="outlined"
                  sx={{ fontSize: '0.7rem' }}
                />
              </Tooltip>
            )}
          </Box>
        }
        action={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <IconButton
              onClick={() => setExpanded(!expanded)}
              aria-expanded={expanded}
              aria-label={expanded ? "Collapse insights" : "Expand insights"}
              sx={{ color: ACE_COLORS.PURPLE }}
            >
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>

            {showRefresh && (
              <IconButton
                onClick={handleRefresh}
                disabled={aiLoading || loading}
                aria-label="Refresh AI insights"
                sx={{ color: ACE_COLORS.PURPLE }}
              >
                {aiLoading || loading ? (
                  <CircularProgress size={20} sx={{ color: ACE_COLORS.PURPLE }} />
                ) : (
                  <RefreshIcon />
                )}
              </IconButton>
            )}
          </Box>
        }
        sx={{
          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
          borderBottom: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}`
        }}
      />

      <Collapse in={expanded} timeout="auto" unmountOnExit>
        {/* Category Filter Chips */}
        <Box sx={{ p: 2, borderBottom: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}` }}>
          <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>
            <Chip
              label="All Categories"
              onClick={() => handleCategoryChange('all')}
              variant={selectedCategory === 'all' ? 'filled' : 'outlined'}
              size="small"
              sx={{
                backgroundColor: selectedCategory === 'all' ? ACE_COLORS.PURPLE : 'transparent',
                color: selectedCategory === 'all' ? ACE_COLORS.WHITE : ACE_COLORS.PURPLE,
                borderColor: ACE_COLORS.PURPLE
              }}
            />
            {Object.entries(INSIGHT_CATEGORIES).map(([key, config]) => (
              <Chip
                key={key}
                label={config.label}
                onClick={() => handleCategoryChange(key)}
                variant={selectedCategory === key ? 'filled' : 'outlined'}
                size="small"
                icon={<config.icon sx={{ fontSize: 16 }} />}
                sx={{
                  backgroundColor: selectedCategory === key ? config.color : 'transparent',
                  color: selectedCategory === key ? ACE_COLORS.WHITE : config.color,
                  borderColor: config.color
                }}
              />
            ))}
          </Stack>
        </Box>

        <CardContent
          sx={{
            flexGrow: 1,
            p: 0
          }}
        >
          {/* Loading state */}
          {(aiLoading || loading) && (
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                minHeight: "200px",
                p: 3
              }}
            >
              <CircularProgress sx={{ color: ACE_COLORS.PURPLE, mb: 2 }} />
              <Typography variant="body2" color="text.secondary">
                Generating AI insights from your analytics data...
              </Typography>
            </Box>
          )}

          {/* Error state */}
          {(aiError || error) && !aiLoading && !loading && (
            <Box sx={{ p: 3, textAlign: "center" }}>
              <WarningIcon sx={{ fontSize: 48, color: 'error.main', mb: 2 }} />
              <Typography color="error" variant="h6" gutterBottom>
                Unable to Generate Insights
              </Typography>
              <Typography color="text.secondary" variant="body2" sx={{ mb: 2 }}>
                {aiError || error?.message || "Failed to load AI insights"}
              </Typography>
              <Button
                onClick={handleRefresh}
                variant="contained"
                size="small"
                sx={{
                  backgroundColor: ACE_COLORS.PURPLE,
                  '&:hover': { backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8) }
                }}
              >
                Try Again
              </Button>
            </Box>
          )}

          {/* Enhanced AI Insights Display */}
          {!aiLoading && !loading && !aiError && !error && processedInsights.length > 0 && (
            <Box sx={{ p: 2 }}>
              {/* Summary Section */}
              {aiInsights?.summary && (
                <Paper
                  sx={{
                    p: 2,
                    mb: 2,
                    backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                    border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`
                  }}
                >
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1, color: ACE_COLORS.DARK }}>
                    📊 Performance Summary
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {aiInsights.summary.overallPerformance}
                  </Typography>
                  {aiInsights.summary.nextSteps && (
                    <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
                      💡 Next Steps: {aiInsights.summary.nextSteps}
                    </Typography>
                  )}
                </Paper>
              )}

              {/* Insights List */}
              <Stack spacing={2}>
                {processedInsights.map((insight, index) => {
                  const categoryConfig = INSIGHT_CATEGORIES[insight.category] || INSIGHT_CATEGORIES.content;
                  const typeConfig = INSIGHT_TYPES[insight.type] || INSIGHT_TYPES.neutral;

                  return (
                    <Paper
                      key={insight.id || index}
                      sx={{
                        p: 2,
                        border: `1px solid ${alpha(categoryConfig.color, 0.2)}`,
                        backgroundColor: alpha(categoryConfig.color, 0.05),
                        '&:hover': {
                          backgroundColor: alpha(categoryConfig.color, 0.1),
                          transform: 'translateY(-1px)',
                          boxShadow: theme.shadows[4]
                        },
                        transition: 'all 0.2s ease-in-out'
                      }}
                    >
                      {/* Insight Header */}
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, mb: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <categoryConfig.icon sx={{ color: categoryConfig.color, fontSize: 20 }} />
                          <typeConfig.icon sx={{ color: typeConfig.color, fontSize: 16 }} />
                        </Box>

                        <Box sx={{ flexGrow: 1 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                              {insight.title}
                            </Typography>
                            <Chip
                              label={insight.category}
                              size="small"
                              sx={{
                                backgroundColor: alpha(categoryConfig.color, 0.2),
                                color: categoryConfig.color,
                                fontSize: '0.7rem',
                                height: 20
                              }}
                            />
                          </Box>

                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                            {insight.description}
                          </Typography>

                          {/* Confidence and Impact Indicators */}
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                            <Tooltip title="AI Confidence Score">
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <Typography variant="caption" color="text.secondary">
                                  Confidence:
                                </Typography>
                                <LinearProgress
                                  variant="determinate"
                                  value={insight.confidence * 100}
                                  sx={{
                                    width: 60,
                                    height: 4,
                                    backgroundColor: alpha(categoryConfig.color, 0.2),
                                    '& .MuiLinearProgress-bar': {
                                      backgroundColor: categoryConfig.color
                                    }
                                  }}
                                />
                                <Typography variant="caption" sx={{ fontWeight: 500 }}>
                                  {Math.round(insight.confidence * 100)}%
                                </Typography>
                              </Box>
                            </Tooltip>

                            <Chip
                              label={`${insight.impact} impact`}
                              size="small"
                              variant="outlined"
                              sx={{
                                borderColor: insight.impact === 'high' ? 'error.main' :
                                           insight.impact === 'medium' ? 'warning.main' : 'info.main',
                                color: insight.impact === 'high' ? 'error.main' :
                                       insight.impact === 'medium' ? 'warning.main' : 'info.main',
                                fontSize: '0.7rem',
                                height: 20
                              }}
                            />
                          </Box>

                          {/* Data Source */}
                          {insight.metrics?.dataSource && (
                            <Typography variant="caption" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                              Source: {insight.metrics.dataSource}
                            </Typography>
                          )}
                        </Box>
                      </Box>

                      {/* Action Buttons */}
                      {insight.actions && insight.actions.length > 0 && (
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                            Recommended Actions:
                          </Typography>
                          <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>
                            {insight.actions.slice(0, 2).map((action, actionIndex) => (
                              <Button
                                key={actionIndex}
                                size="small"
                                variant="outlined"
                                startIcon={<ActionIcon sx={{ fontSize: 14 }} />}
                                onClick={() => handleActionClick(action, insight)}
                                sx={{
                                  borderColor: categoryConfig.color,
                                  color: categoryConfig.color,
                                  fontSize: '0.75rem',
                                  py: 0.5,
                                  px: 1,
                                  '&:hover': {
                                    backgroundColor: alpha(categoryConfig.color, 0.1),
                                    borderColor: categoryConfig.color
                                  }
                                }}
                              >
                                {action.title}
                              </Button>
                            ))}
                            {insight.actions.length > 2 && (
                              <Button
                                size="small"
                                variant="text"
                                onClick={() => setShowActions(!showActions)}
                                sx={{ color: 'text.secondary', fontSize: '0.75rem' }}
                              >
                                +{insight.actions.length - 2} more
                              </Button>
                            )}
                          </Stack>
                        </Box>
                      )}
                    </Paper>
                  );
                })}
              </Stack>

              {/* Plan Upgrade Prompt for Creator Plan */}
              {subscription?.plan_id === 'creator' && (
                <Paper
                  sx={{
                    p: 2,
                    mt: 2,
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
                    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
                    textAlign: 'center'
                  }}
                >
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                    🚀 Unlock Advanced AI Insights
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Upgrade to Accelerator or Dominator for competitive intelligence, predictive analytics, and unlimited insights.
                  </Typography>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{
                      backgroundColor: ACE_COLORS.PURPLE,
                      '&:hover': { backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8) }
                    }}
                  >
                    Upgrade Plan
                  </Button>
                </Paper>
              )}
            </Box>
          )}

          {/* Empty state */}
          {!aiLoading && !loading && !aiError && !error && processedInsights.length === 0 && (
            <Box sx={{ p: 3, textAlign: "center" }}>
              <AIIcon
                sx={{
                  fontSize: 48,
                  color: ACE_COLORS.PURPLE,
                  mb: 2,
                  opacity: 0.6
                }}
              />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No AI insights available
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                We need more analytics data to generate meaningful AI recommendations.
                Check back after posting some content!
              </Typography>
              <Button
                onClick={handleRefresh}
                variant="outlined"
                size="small"
                sx={{
                  borderColor: ACE_COLORS.PURPLE,
                  color: ACE_COLORS.PURPLE
                }}
              >
                Refresh Insights
              </Button>
            </Box>
          )}
        </CardContent>
      </Collapse>
    </Card>
  );
});

// Display name for debugging
QuickInsights.displayName = 'QuickInsights';

// Enhanced PropTypes validation for enterprise-grade component
QuickInsights.propTypes = {
  /** Analytics data from dashboard */
  data: PropTypes.object,

  /** Refresh callback function */
  onRefresh: PropTypes.func,

  /** Loading state indicator */
  loading: PropTypes.bool,

  /** Error object or message */
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),

  /** Show refresh button */
  showRefresh: PropTypes.bool,

  /** Maximum number of insights to display */
  maxInsights: PropTypes.number,

  /** Enable automatic refresh */
  autoRefresh: PropTypes.bool,

  /** Auto refresh interval in milliseconds */
  refreshInterval: PropTypes.number,

  /** Subscription plan features configuration */
  planFeatures: PropTypes.shape({
    maxInsights: PropTypes.number,
    hasAdvancedAnalytics: PropTypes.bool,
    hasCompetitiveIntelligence: PropTypes.bool,
    hasPredictiveAnalytics: PropTypes.bool
  }),

  /** Enable enhanced accessibility features */
  enableAccessibility: PropTypes.bool,

  /** Test identifier for automated testing */
  testId: PropTypes.string
};

export default QuickInsights;
