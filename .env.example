# Environment Configuration Template for ACE Social
# Copy this file to .env and fill in your actual values

# ================================
# APPLICATION SETTINGS
# ================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
PROJECT_NAME=ACE Social
PROJECT_VERSION=1.0.0

# ================================
# SECURITY SETTINGS
# ================================
# Generate with: openssl rand -hex 32
SECRET_KEY=your-super-secret-key-here-at-least-32-characters-long
JWT_SECRET_KEY=your-jwt-secret-key-here-at-least-32-characters-long
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7

# ================================
# DATABASE CONFIGURATION
# ================================
# MongoDB
MONGODB_URL=**********************************************************
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=password123
MONGO_DATABASE=ace_social_dev

# Redis
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=redis123

# ================================
# EXTERNAL API KEYS
# ================================
# OpenAI for AI content generation
OPENAI_API_KEY=your-openai-api-key-here

# Frontend OpenAI Configuration (for AI Insights)
REACT_APP_OPENAI_API_URL=https://api.openai.com/v1
REACT_APP_AI_INSIGHTS_ENABLED=true
REACT_APP_LOG_LEVEL=INFO
REACT_APP_ANALYTICS_ENABLED=true
REACT_APP_PERFORMANCE_TRACKING=true

# SendGrid for email services
SENDGRID_API_KEY=your-sendgrid-api-key-here
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=ACE Social

# Lemon Squeezy for payment processing
LEMON_SQUEEZY_API_KEY=your-lemon-squeezy-api-key-here
LEMON_SQUEEZY_STORE_ID=your-store-id-here
LEMON_SQUEEZY_WEBHOOK_SECRET=your-webhook-secret-here

# ================================
# FRONTEND CONFIGURATION
# ================================
FRONTEND_URL=http://localhost:3000
VITE_API_URL=http://localhost:8000
VITE_ENVIRONMENT=development

# ================================
# CORS SETTINGS
# ================================
CORS_ORIGINS=["http://localhost:3000","http://localhost:5173","http://localhost:8080"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["GET","POST","PUT","DELETE","OPTIONS","PATCH"]
CORS_ALLOW_HEADERS=["*"]

# ================================
# RATE LIMITING
# ================================
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# ================================
# CACHING
# ================================
CACHE_ENABLED=true
CACHE_BACKEND=redis
CACHE_TTL=3600

# ================================
# FILE UPLOAD SETTINGS
# ================================
MAX_UPLOAD_SIZE=104857600
UPLOAD_DIR=/app/uploads
ALLOWED_FILE_TYPES=["jpg","jpeg","png","gif","pdf","doc","docx"]

# ================================
# IMAGE METADATA REMOVAL SETTINGS
# ================================
METADATA_REMOVAL_ENABLED=true
METADATA_REMOVAL_PRESERVE_QUALITY=true
METADATA_REMOVAL_MAX_FILE_SIZE_MB=50
METADATA_REMOVAL_CACHE_TTL=3600
METADATA_REMOVAL_PERFORMANCE_TARGET_MS=200
METADATA_REMOVAL_BATCH_SIZE_LIMIT=10
METADATA_REMOVAL_TRACK_USAGE=true

# ================================
# MONITORING & LOGGING
# ================================
# Grafana
GRAFANA_PASSWORD=admin123

# Health Check
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# ================================
# SOCIAL MEDIA INTEGRATIONS
# ================================
# Twitter/X API
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret
TWITTER_ACCESS_TOKEN=your-twitter-access-token
TWITTER_ACCESS_TOKEN_SECRET=your-twitter-access-token-secret

# LinkedIn API
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

# Facebook/Meta API
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# Instagram API
INSTAGRAM_CLIENT_ID=your-instagram-client-id
INSTAGRAM_CLIENT_SECRET=your-instagram-client-secret

# ================================
# ANALYTICS & TRACKING
# ================================
GOOGLE_ANALYTICS_ID=your-google-analytics-id
MIXPANEL_TOKEN=your-mixpanel-token

# ================================
# DEVELOPMENT SETTINGS
# ================================
# Set to true for development
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1
WATCHFILES_FORCE_POLLING=true
CHOKIDAR_USEPOLLING=true

# ================================
# PRODUCTION SETTINGS
# ================================
# SSL/TLS
SSL_ENABLED=false
SSL_CERT_PATH=/etc/ssl/certs/cert.pem
SSL_KEY_PATH=/etc/ssl/private/key.pem

# Domain
DOMAIN=yourdomain.com
SUBDOMAIN=app

# CDN
CDN_URL=https://cdn.yourdomain.com

# ================================
# BACKUP SETTINGS
# ================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
